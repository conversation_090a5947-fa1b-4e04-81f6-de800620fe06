import type { ComponentType } from '#/adapter/component';

/**
 * 联动条件类型定义
 */
export type DependencyCondition =
  | ((values: Record<string, any>) => boolean)
  | ((values: Record<string, any>) => Promise<boolean>)
  | boolean;

/**
 * 编辑权限控制类型
 */
export type EditPermissionType =
  | 'add-only' // 只有新增时可以填写，编辑时不可填写
  | 'both' // 新增和编辑都可以填写（默认）
  | 'edit-only' // 只有编辑时可以填写，新增时不可填写
  | 'none'; // 新增和编辑都不可填写

/**
 * 联动赋值配置
 */
export interface LinkageAssignmentConfig {
  /** 目标字段映射：当前字段选择值后，给其他字段赋值 */
  targetFields: {
    /** 是否清空目标字段（当当前字段清空时） */
    clearOnEmpty?: boolean;
    /** 目标字段名 */
    field: string;
    /** 动态选项配置：为目标字段设置选项数据 */
    options?: {
      /** 是否自动选择第一个选项 */
      autoSelect?: boolean;
      /** 是否清空当前值（当选项变化时） */
      clearValue?: boolean;
      /** 默认选项（当没有匹配时使用） */
      default?: Array<{ [key: string]: any; label: string; value: any }>;
      /** 选项映射：根据当前字段选择值获取对应选项 */
      mapping: Record<
        string,
        Array<{ [key: string]: any; label: string; value: any }>
      >;
    };
    /** 值映射函数或固定值 */
    valueMapping: ((selectedValue: any, selectedOption: any) => any) | any;
  }[];
}

/**
 * 字段配置选项
 */
export interface FieldConfig {
  /** 验证规则类型 */
  ruleType?: 'required' | 'selectRequired';
  /** 占位符 */
  placeholder?: string | string[];
  /** 选项数据（用于 select、radio、checkbox 等） - 支持数组和对象格式 */
  options?:
    | Array<{
        children?: any[];
        disabled?: boolean;
        label: string;
        value: any;
      }>
    | Record<
        string,
        {
          [key: string]: any;
          disabled?: boolean;
          key?: string;
          label?: string;
          name?: string;
          num?: number;
          value?: any;
        }
      >;
  /** 树形数据（用于 TreeSelect） */
  treeData?: Array<{
    children?: any[];
    label: string;
    value: any;
  }>;
  /** 是否允许清空 */
  allowClear?: boolean;
  /** 是否显示搜索（用于 Select） */
  showSearch?: boolean;
  /** 是否支持过滤（用于 Select） */
  filterOption?: boolean;
  /** 最小值（用于 InputNumber） */
  min?: number;
  /** 最大值（用于 InputNumber） */
  max?: number;
  /** 精度（用于 InputNumber） */
  precision?: number;
  /** 后缀内容 */
  suffix?: string;
  /** 前缀内容 */
  prefix?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 表单项样式类 */
  formItemClass?: string;
  /** 其他组件属性 */
  componentProps?: Record<string, any>;

  // API 相关配置（用于 apiSelect 和 apiTree）
  /** API 请求地址（推荐使用，替代 api 函数） */
  url?: string;
  /** API 函数（兼容旧版本） */
  api?: (...args: any[]) => any;
  /** API 请求参数 */
  params?: Record<string, any>;
  /** 显示字段名 */
  labelField?: string;
  /** 值字段名 */
  valueField?: string;
  /** 子节点字段名（用于 apiTree） */
  childrenField?: string;
  /** 从 API 结果中提取数组的字段路径 */
  resultField?: string;
  /** 是否立即加载 */
  immediate?: boolean;
  /** 每次显示时都重新加载 */
  alwaysLoad?: boolean;
  /** 自动选择选项 */
  autoSelect?: 'first' | 'last' | 'one' | ((...args: any[]) => any) | false;
  /** 请求前处理参数 */
  beforeFetch?: (...args: any[]) => any;
  /** 请求后处理结果 */
  afterFetch?: (...args: any[]) => any;
  /** 自定义标签格式化函数，用于自定义选项显示格式 */
  labelFormatter?: (item: any) => string;
  /** 默认展开所有节点（用于 apiTree） */
  treeDefaultExpandAll?: boolean;
  /** 树节点过滤函数（用于 apiTree） */
  filterTreeNode?: (...args: any[]) => any;
  /** 是否启用搜索功能（用于 apiSelect 和 apiTree） */
  searchable?: boolean;
  /** 搜索参数名，默认为 'search' */
  searchParamName?: string;
  /** 搜索字段名（兼容旧版本） */
  searchFieldName?: string;
  /** 是否禁用搜索请求 */
  disableSearchRequest?: boolean;
  /** 是否启用分页（用于 apiTree） */
  pagination?: boolean;
  /** 分页大小 */
  pageSize?: number;
  /** 分页大小（兼容小写格式） */
  pagesize?: number;
  /** 是否启用加载更多 */
  loadMore?: boolean;
  /** 页码参数名 */
  pageParamName?: string;
  /** 页大小参数名 */
  pageSizeParamName?: string;
  /** 回显数据时用于传递参数的字段名 */
  returnParamsField?: string;

  /** 编辑权限控制：控制字段在新增/编辑模式下的可编辑性 */
  editPermission?: EditPermissionType;

  /** 联动赋值配置：当前字段选择值后给其他字段赋值 */
  linkageAssignment?: LinkageAssignmentConfig;

  // Radio 和 Checkbox 相关配置
  /** Radio 按钮类型 */
  optionType?: 'button' | 'default';
  /** Radio 按钮样式（当 optionType 为 'button' 时） */
  buttonStyle?: 'outline' | 'solid';

  // Select 相关配置
  /** 是否多选（会转换为 mode: 'multiple'） */
  multiple?: boolean;
  /** 选择模式 */
  mode?: 'combobox' | 'multiple' | 'tags';
  /** 多选时最多显示多少个 tag */
  maxTagCount?: number;
  /** 多选时每个 tag 的最大长度 */
  maxTagTextLength?: number;
  /** 多选时超出 maxTagCount 时的占位符 */
  maxTagPlaceholder?: ((omittedValues: any[]) => string) | string;

  // TreeSelect 相关配置
  /** 是否显示复选框 */
  treeCheckable?: boolean;
  /** 父子节点选中状态不再关联 */
  treeCheckStrictly?: boolean;

  // 联动配置
  /** 联动配置 */
  linkage?: LinkageConfig;

  /** 字段映射时间配置（用于dateRange类型） */
  fieldMappingTime?: Array<[string, string[], string[]]>;

  /** 计算字段配置（用于calculated类型） */
  calculation?: {
    /** 计算类型 */
    type: 'sum' | 'average' | 'product' | 'max' | 'min' | 'count' | 'formula';
    /** 依赖的表格字段 */
    sourceFields: string[];
    /** 过滤条件（可选） */
    filter?: Array<{
      field: string;
      operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'in' | 'not_in';
      value: any;
    }>;
    /** 自定义公式（当type为formula时使用） */
    formula?: string;
    /** 小数位数（可选，默认2位） */
    precision?: number;
    /** 默认值（当计算结果为空时使用） */
    defaultValue?: any;
  };

  /** 计算字段防抖配置 */
  debounce?: boolean;
  /** 防抖延迟时间（毫秒，默认300） */
  debounceDelay?: number;
}

/**
 * 条件操作符类型
 */
export type ConditionOperator =
  | 'contains'
  | 'custom'
  | 'ends_with'
  | 'equals'
  | 'greater'
  | 'greater_equal'
  | 'in'
  | 'is_empty'
  | 'is_not_empty'
  | 'is_required'
  | 'less'
  | 'less_equal'
  | 'not_contains'
  | 'not_equals'
  | 'not_in'
  | 'not_regex'
  | 'regex'
  | 'starts_with';

/**
 * 基础条件配置
 */
export interface BaseCondition {
  /** 触发字段名 */
  field: string;
  /** 条件操作符 */
  operator: ConditionOperator;
  /** 比较值 */
  value: any;
}

/**
 * 显示/隐藏规则（DOM 级别）
 */
export interface VisibilityRule {
  /** 显示条件：当满足条件时显示字段 */
  showWhen?: BaseCondition[];
  /** 隐藏条件：当满足条件时隐藏字段 */
  hideWhen?: BaseCondition[];
}

/**
 * 显示/隐藏规则（CSS 级别）
 */
export interface DisplayRule {
  /** 显示条件：当满足条件时显示字段 */
  showWhen?: BaseCondition[];
  /** 隐藏条件：当满足条件时隐藏字段 */
  hideWhen?: BaseCondition[];
}

/**
 * 禁用规则
 */
export interface DisabledRule {
  /** 禁用条件：当满足条件时禁用字段 */
  disableWhen?: BaseCondition[];
  /** 启用条件：当满足条件时启用字段 */
  enableWhen?: BaseCondition[];
}

/**
 * 必填规则
 */
export interface RequiredRule {
  /** 必填条件：当满足条件时字段为必填 */
  requiredWhen?: BaseCondition[];
  /** 可选条件：当满足条件时字段为可选 */
  optionalWhen?: BaseCondition[];
}

/**
 * 动态选项规则
 */
export interface OptionsRule {
  /** 选项映射：根据触发字段值获取对应选项 */
  mapping: Record<string, Array<{ label: string; value: any }>>;
  /** 默认选项（当没有匹配时使用） */
  default?: Array<{ label: string; value: any }>;
}

/**
 * 动态组件属性规则
 */
export interface ComponentPropsRule {
  /** 属性映射：根据触发字段值设置组件属性 */
  mapping: Record<string, Record<string, any>>;
  /** 默认属性 */
  default?: Record<string, any>;
}

/**
 * 联动规则集合
 */
export interface LinkageRules {
  /** 字段显示/隐藏规则（DOM 级别） */
  visibility?: VisibilityRule;
  /** 字段显示/隐藏规则（CSS 级别） */
  display?: DisplayRule;
  /** 字段禁用规则 */
  disabled?: DisabledRule;
  /** 字段必填规则 */
  required?: RequiredRule;
  /** 动态选项规则 */
  options?: OptionsRule;
  /** 动态组件属性规则 */
  componentProps?: ComponentPropsRule;
}

/**
 * 联动配置
 */
export interface LinkageConfig {
  /** 触发字段列表 */
  triggerFields: string[];
  /** 联动规则配置 */
  rules: LinkageRules;
}

/**
 * 后端搜索条件字段定义（重构版本）
 * 必传字段和常用字段在外层，其他配置项包装在 config 对象中
 */
export interface BackendSearchItem {
  /** 字段名（必传） */
  field: string;
  /** 显示标题（必传） */
  title: string;
  /** 字段类型（必传），用于映射到不同的 component */
  type:
    | 'apiSelect'
    | 'apiselect'
    | 'apiTree'
    | 'calculated'
    | 'checkbox'
    | 'date'
    | 'dateRange'
    | 'edittable'
    | 'hidden'
    | 'input'
    | 'mentions'
    | 'number'
    | 'password'
    | 'radio'
    | 'rate'
    | 'select'
    | 'switch'
    | 'text'
    | 'textarea'
    | 'time'
    | 'tree'
    | 'Upload';
  /** 是否唯一（可选，外层） */
  isonly?: boolean;
  /** 是否必填（可选，外层） */
  required?: boolean;
  /** 默认值（可选，外层） */
  default?: any;
  /** 是否禁用（可选，外层） */
  disabled?: boolean;

  /** 字段配置选项（可选） */
  config?: FieldConfig;
  /** 联动配置（可选） */
  linkage?: LinkageConfig;
  /** 是否显示字段（可选） */
  ifShow?: ((values: Record<string, any>) => boolean) | boolean;
}

/**
 * 分组搜索数据格式
 */
export interface GroupedSearchData {
  label?: string;
  dataItem: BackendSearchItem[];
}

/**
 * 包装的分组搜索数据格式
 */
export interface DirectGroupedSearchData {
  schema: GroupedSearchData[];
}

/**
 * 类型到组件的映射关系
 */
export const TYPE_TO_COMPONENT_MAP: Record<string, ComponentType> = {
  input: 'Input',
  text: 'Input', // text 类型映射到 Input 组件
  password: 'InputPassword',
  textarea: 'Textarea',
  number: 'InputNumber',
  select: 'Select',
  apiSelect: 'ApiSelect', // API 下拉选择组件
  apiselect: 'ApiSelect', // API 下拉选择组件（小写兼容）
  radio: 'RadioGroup',
  checkbox: 'CheckboxGroup',
  date: 'DatePicker',
  dateRange: 'RangePicker',
  time: 'TimePicker',
  tree: 'TreeSelect',
  apiTree: 'ApiTreeSelect', // API 树形选择组件
  switch: 'Switch',
  rate: 'Rate',
  mentions: 'Mentions',
  Upload: 'Upload',
  edittable: 'EditTable',
  hidden: 'Input', // hidden 类型映射到 Input 组件，但会被隐藏
  groupTitle: 'GroupTitle', // 分组标题组件
};
