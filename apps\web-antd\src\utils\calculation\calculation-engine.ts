/**
 * 表格字段计算引擎
 * 支持各种数学运算和自定义公式计算
 */

export interface CalculationRule {
  /** 计算类型 */
  type: 'sum' | 'average' | 'product' | 'max' | 'min' | 'count' | 'formula';
  /** 依赖的表格字段 */
  sourceFields: string[];
  /** 过滤条件（可选） */
  filter?: {
    field: string;
    operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'in' | 'not_in';
    value: any;
  }[];
  /** 自定义公式（当type为formula时使用） */
  formula?: string;
  /** 小数位数（可选，默认2位） */
  precision?: number;
  /** 默认值（当计算结果为空时使用） */
  defaultValue?: any;
}

export interface CalculationConfig {
  /** 目标字段名 */
  targetField: string;
  /** 计算规则 */
  rule: CalculationRule;
  /** 是否启用防抖（默认true） */
  debounce?: boolean;
  /** 防抖延迟时间（毫秒，默认300） */
  debounceDelay?: number;
}

/**
 * 计算引擎类
 */
export class CalculationEngine {
  private static instance: CalculationEngine;
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();

  static getInstance(): CalculationEngine {
    if (!CalculationEngine.instance) {
      CalculationEngine.instance = new CalculationEngine();
    }
    return CalculationEngine.instance;
  }

  /**
   * 执行计算
   * @param tableData 表格数据
   * @param config 计算配置
   * @returns 计算结果
   */
  calculate(tableData: any[], config: CalculationConfig): any {
    const { rule } = config;

    try {
      // 过滤数据
      let filteredData = this.filterData(tableData, rule.filter);

      // 提取计算字段的值
      const values = this.extractValues(filteredData, rule.sourceFields);

      // 执行计算
      let result = this.executeCalculation(values, rule);

      // 处理精度（解决JavaScript浮点数精度问题）
      if (typeof result === 'number' && rule.precision !== undefined) {
        result = this.roundToPrecision(result, rule.precision);
      }

      // 处理默认值
      if (
        (result === null ||
          result === undefined ||
          (typeof result === 'number' && isNaN(result))) &&
        rule.defaultValue !== undefined
      ) {
        result = rule.defaultValue;
      }

      return result;
    } catch (error) {
      console.warn(`计算字段 ${config.targetField} 时发生错误:`, error);
      return config.rule.defaultValue ?? null;
    }
  }

  /**
   * 带防抖的计算
   * @param tableData 表格数据
   * @param config 计算配置
   * @param callback 计算完成回调
   */
  calculateWithDebounce(
    tableData: any[],
    config: CalculationConfig,
    callback: (result: any) => void,
  ): void {
    const { targetField, debounce = true, debounceDelay = 300 } = config;

    if (!debounce) {
      const result = this.calculate(tableData, config);
      callback(result);
      return;
    }

    // 清除之前的定时器
    const existingTimer = this.debounceTimers.get(targetField);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // 设置新的定时器
    const timer = setTimeout(() => {
      const result = this.calculate(tableData, config);
      callback(result);
      this.debounceTimers.delete(targetField);
    }, debounceDelay);

    this.debounceTimers.set(targetField, timer);
  }

  /**
   * 过滤数据
   */
  private filterData(data: any[], filters?: CalculationRule['filter']): any[] {
    if (!filters || filters.length === 0) {
      return data;
    }

    return data.filter((row) => {
      return filters.every((filter) => {
        const fieldValue = row[filter.field];
        const filterValue = filter.value;

        switch (filter.operator) {
          case '=':
            return fieldValue === filterValue;
          case '!=':
            return fieldValue !== filterValue;
          case '>':
            return Number(fieldValue) > Number(filterValue);
          case '<':
            return Number(fieldValue) < Number(filterValue);
          case '>=':
            return Number(fieldValue) >= Number(filterValue);
          case '<=':
            return Number(fieldValue) <= Number(filterValue);
          case 'in':
            return (
              Array.isArray(filterValue) && filterValue.includes(fieldValue)
            );
          case 'not_in':
            return (
              Array.isArray(filterValue) && !filterValue.includes(fieldValue)
            );
          default:
            return true;
        }
      });
    });
  }

  /**
   * 提取计算字段的值
   */
  private extractValues(data: any[], sourceFields: string[]): number[][] {
    return sourceFields.map((field) => {
      return data
        .map((row) => {
          const value = row[field];
          const numValue = Number(value);
          return isNaN(numValue) ? 0 : numValue;
        })
        .filter((val) => !isNaN(val));
    });
  }

  /**
   * 执行具体的计算逻辑
   */
  private executeCalculation(values: number[][], rule: CalculationRule): any {
    if (values.length === 0) {
      return rule.defaultValue ?? null;
    }

    switch (rule.type) {
      case 'sum':
        return this.calculateSum(values);
      case 'average':
        return this.calculateAverage(values);
      case 'product':
        return this.calculateProduct(values);
      case 'max':
        return this.calculateMax(values);
      case 'min':
        return this.calculateMin(values);
      case 'count':
        return this.calculateCount(values);
      case 'formula':
        return this.calculateFormula(values, rule.formula || '');
      default:
        throw new Error(`不支持的计算类型: ${rule.type}`);
    }
  }

  /**
   * 求和计算（使用精确计算避免浮点数精度问题）
   */
  private calculateSum(values: number[][]): number {
    if (values.length === 1) {
      // 单字段求和
      return values[0].reduce((sum, val) => this.safeAdd(sum, val), 0);
    } else {
      // 多字段求和：每行对应字段相加，然后求总和
      const rowSums = this.getRowWiseValues(values).map((rowValues) =>
        rowValues.reduce((sum, val) => this.safeAdd(sum, val), 0),
      );
      return rowSums.reduce((sum, val) => this.safeAdd(sum, val), 0);
    }
  }

  /**
   * 平均值计算
   */
  private calculateAverage(values: number[][]): number {
    const sum = this.calculateSum(values);
    const count = this.calculateCount(values);
    return count > 0 ? sum / count : 0;
  }

  /**
   * 乘积计算（使用精确计算避免浮点数精度问题）
   */
  private calculateProduct(values: number[][]): number {
    if (values.length === 1) {
      // 单字段乘积
      return values[0].reduce(
        (product, val) => this.safeMultiply(product, val),
        1,
      );
    } else {
      // 多字段乘积：每行对应字段相乘，然后求总乘积
      const rowProducts = this.getRowWiseValues(values).map((rowValues) =>
        rowValues.reduce((product, val) => this.safeMultiply(product, val), 1),
      );
      return rowProducts.reduce(
        (product, val) => this.safeMultiply(product, val),
        1,
      );
    }
  }

  /**
   * 最大值计算
   */
  private calculateMax(values: number[][]): number {
    const allValues = values.flat();
    return allValues.length > 0 ? Math.max(...allValues) : 0;
  }

  /**
   * 最小值计算
   */
  private calculateMin(values: number[][]): number {
    const allValues = values.flat();
    return allValues.length > 0 ? Math.min(...allValues) : 0;
  }

  /**
   * 计数
   */
  private calculateCount(values: number[][]): number {
    if (values.length === 1) {
      return values[0].length;
    } else {
      return this.getRowWiseValues(values).length;
    }
  }

  /**
   * 自定义公式计算
   */
  private calculateFormula(values: number[][], formula: string): any {
    try {
      // 创建安全的计算环境
      const context = this.createFormulaContext(values);

      // 替换公式中的变量
      let processedFormula = formula;
      Object.keys(context).forEach((key) => {
        const regex = new RegExp(`\\b${key}\\b`, 'g');
        processedFormula = processedFormula.replace(
          regex,
          context[key].toString(),
        );
      });

      // 安全执行公式（仅支持基本数学运算）
      return this.safeEvaluate(processedFormula);
    } catch (error) {
      console.warn('公式计算失败:', error);
      return 0;
    }
  }

  /**
   * 获取按行排列的值（用于多字段计算）
   */
  private getRowWiseValues(values: number[][]): number[][] {
    if (values.length === 0) return [];

    const maxLength = Math.max(...values.map((arr) => arr.length));
    const result: number[][] = [];

    for (let i = 0; i < maxLength; i++) {
      const rowValues: number[] = [];
      for (let j = 0; j < values.length; j++) {
        if (i < values[j].length) {
          rowValues.push(values[j][i]);
        }
      }
      if (rowValues.length > 0) {
        result.push(rowValues);
      }
    }

    return result;
  }

  /**
   * 创建公式计算上下文
   */
  private createFormulaContext(values: number[][]): Record<string, number> {
    const context: Record<string, number> = {};

    // 为每个字段创建变量
    values.forEach((fieldValues, index) => {
      const fieldName = `field${index + 1}`;
      context[`${fieldName}_sum`] = fieldValues.reduce(
        (sum, val) => sum + val,
        0,
      );
      context[`${fieldName}_avg`] =
        fieldValues.length > 0
          ? context[`${fieldName}_sum`] / fieldValues.length
          : 0;
      context[`${fieldName}_count`] = fieldValues.length;
      context[`${fieldName}_max`] =
        fieldValues.length > 0 ? Math.max(...fieldValues) : 0;
      context[`${fieldName}_min`] =
        fieldValues.length > 0 ? Math.min(...fieldValues) : 0;
    });

    return context;
  }

  /**
   * 安全的公式执行（仅支持基本数学运算）
   */
  private safeEvaluate(formula: string): number {
    // 移除所有非数学运算符和数字的字符
    const safeFormula = formula.replace(/[^0-9+\-*/.() ]/g, '');

    // 使用Function构造器安全执行
    try {
      return new Function(`return ${safeFormula}`)();
    } catch {
      return 0;
    }
  }

  /**
   * 精确的数字精度处理（解决JavaScript浮点数精度问题）
   * @param num 要处理的数字
   * @param precision 精度位数
   * @returns 处理后的数字
   */
  private roundToPrecision(num: number, precision: number): number {
    if (isNaN(num) || !isFinite(num)) {
      return 0;
    }

    // 使用更精确的方法处理浮点数精度问题
    const factor = Math.pow(10, precision);
    return Math.round((num + Number.EPSILON) * factor) / factor;
  }

  /**
   * 安全的数字加法（避免精度问题）
   */
  private safeAdd(a: number, b: number): number {
    const precision = Math.max(
      this.getDecimalPlaces(a),
      this.getDecimalPlaces(b),
    );
    const factor = Math.pow(10, precision);
    return (Math.round(a * factor) + Math.round(b * factor)) / factor;
  }

  /**
   * 安全的数字乘法（避免精度问题）
   */
  private safeMultiply(a: number, b: number): number {
    const precisionA = this.getDecimalPlaces(a);
    const precisionB = this.getDecimalPlaces(b);
    const factor = Math.pow(10, precisionA + precisionB);
    return (
      (Math.round(a * Math.pow(10, precisionA)) *
        Math.round(b * Math.pow(10, precisionB))) /
      factor
    );
  }

  /**
   * 获取数字的小数位数
   */
  private getDecimalPlaces(num: number): number {
    if (Math.floor(num) === num) return 0;
    const str = num.toString();
    if (str.indexOf('.') !== -1 && str.indexOf('e-') === -1) {
      return str.split('.')[1].length;
    } else if (str.indexOf('e-') !== -1) {
      const parts = str.split('e-');
      return parseInt(parts[1], 10);
    }
    return 0;
  }

  /**
   * 清理防抖定时器
   */
  clearDebounceTimers(): void {
    this.debounceTimers.forEach((timer) => clearTimeout(timer));
    this.debounceTimers.clear();
  }
}

// 导出单例实例
export const calculationEngine = CalculationEngine.getInstance();
